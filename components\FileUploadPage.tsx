'use client';

import React, { useState, useEffect } from 'react';
import FileUploadWithFaiss from './FileUploadWithFaiss';
import AdminSidebar from './adminsidebar';
import { uploadCSVToFaiss, formatFileSize, fetchEmails, checkIndexExists, getEmbeddingModels, createPineCollectionEntry } from '@/services/fileUploadService';

const FileUploadPage: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadStatus, setUploadStatus] = useState<Record<string, any>>({});
  const [processingStages, setProcessingStages] = useState<Record<string, any>>({});
  const [selectedClient, setSelectedClient] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [emails, setEmails] = useState<string[]>([]);
  const [isLoadingEmails, setIsLoadingEmails] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [indexExists, setIndexExists] = useState(false);
  const [isCheckingIndex, setIsCheckingIndex] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [updateMode, setUpdateMode] = useState<string | null>(null);
  const [indexName, setIndexName] = useState('');
  const [embedModel, setEmbedModel] = useState('all-MiniLM-L6-v2');
  const [availableModels, setAvailableModels] = useState<Record<string, any>>({});
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [currentView, setCurrentView] = useState('create');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [apiKeyError, setApiKeyError] = useState('');

  // Fetch emails and embedding models when component mounts
  useEffect(() => {
    const getEmails = async () => {
      setIsLoadingEmails(true);
      setEmailError('');
      try {
        const emailList = await fetchEmails();
        setEmails(emailList);
      } catch (error) {
        console.error('Error fetching emails:', error);
        setEmailError('Failed to load emails. Please try again later.');
      } finally {
        setIsLoadingEmails(false);
      }
    };

    const getModels = async () => {
      try {
        const modelsData = await getEmbeddingModels();
        if (modelsData.success) {
          setAvailableModels(modelsData.models);
        } else {
          // Set fallback models if API response doesn't indicate success
          setAvailableModels({
            "all-MiniLM-L6-v2": {
              "name": "all-MiniLM-L6-v2",
              "description": "Sentence Transformers model for semantic similarity",
              "dimension": 384
            }
          });
        }
      } catch (error) {
        console.error('Error fetching embedding models:', error);
        // Set fallback models when API call fails
        setAvailableModels({
          "all-MiniLM-L6-v2": {
            "name": "all-MiniLM-L6-v2",
            "description": "Sentence Transformers model for semantic similarity",
            "dimension": 384
          }
        });
      }
    };

    getEmails();
    getModels();
  }, []);

  // Check if index exists when index name is provided
  useEffect(() => {
    if (indexName.trim()) {
      checkIfIndexExists();
    }
  }, [indexName]);

  // Define processing stages
  const stages = [
    { id: 'uploading', label: 'Uploading File', description: 'Transferring file to server' },
    { id: 'processing', label: 'Processing Data', description: 'Parsing CSV and preparing vectors' },
    { id: 'indexing', label: 'Creating Index', description: 'Building FAISS vector database' },
    { id: 'complete', label: 'Complete', description: 'Data successfully indexed and ready for queries' }
  ];

  // Check if index exists
  const checkIfIndexExists = async () => {
    if (!indexName.trim()) {
      return;
    }

    // Store the index name in localStorage for immediate use
    if (typeof window !== 'undefined') {
      localStorage.setItem('faiss_index_name', indexName);
      console.log(`Set index_name in localStorage: ${indexName}`);
    }

    setIsCheckingIndex(true);
    try {
      console.log(`Checking if FAISS index exists: ${indexName}`);
      const { exists } = await checkIndexExists(indexName, selectedClient, embedModel);
      setIndexExists(exists);

      if (exists) {
        console.log(`Index ${indexName} exists, showing update modal`);
        console.log(`Index ${indexName} exists, showing update modal`);
        setShowUpdateModal(true);
      } else {
        console.log(`Index ${indexName} does not exist, will create new index`);
        console.log(`Index ${indexName} does not exist, will create new index`);
        // If index doesn't exist, set update mode to null
        setUpdateMode(null);
      }
    } catch (error) {
      console.error('Error checking if index exists:', error);
    } finally {
      setIsCheckingIndex(false);
    }
  };

  // Handle upload cancellation
  const handleStopUpload = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
      if (selectedFile) {
        setUploadStatus(prev => ({
          ...prev,
          [selectedFile.name]: {
            status: 'error',
            progress: 0,
            message: 'Upload cancelled by user'
          }
        }));
      }
    }
  };

  // Handle submit button click
  const handleSubmit = async () => {
    if (!selectedFile) {
      alert('Please select a file first');
      return;
    }

    if (!indexName.trim()) {
      alert('Please provide an index name');
      return;
    }

    // Store the configuration in PINE collection with new structure
    try {
      console.log(`Storing configuration in PINE collection: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${selectedClient}`);

      // Create PINE collection entry with new structure:
      // api_key -> embedding model name
      // index_name -> provided index name
      // client -> email which user selects
      await createPineCollectionEntry(embedModel, indexName, selectedClient);
      console.log('Successfully stored configuration in PINE collection');

      // Also store in localStorage for immediate use
      if (typeof window !== 'undefined') {
        localStorage.setItem('faiss_index_name', indexName);
        localStorage.setItem('faiss_embed_model', embedModel);
        localStorage.setItem('faiss_client_email', selectedClient);
        console.log(`Stored credentials in localStorage: index_name=${indexName}, embed_model=${embedModel}`);
      }
    } catch (error) {
      console.error('Error storing configuration in PINE collection:', error);
      // Continue with upload even if PINE storage fails
    }

    const controller = new AbortController();
    setAbortController(controller);
    if (!selectedFile) {
      alert('Please select a file first');
      return;
    }

    if (!selectedClient) {
      alert('Please select a client');
      return;
    }

    // Print data to console in JSON format
    const data = {
      index_name: indexName,
      email: selectedClient,
      embed_model: embedModel,
      update_mode: updateMode
    };

    console.log(JSON.stringify(data, null, 2));

    // Add the file to the uploadedFiles state
    setUploadedFiles([selectedFile]);

    // Set initial upload status
    setUploadStatus({
      [selectedFile.name]: {
        status: 'uploading',
        progress: 0,
        message: `Starting upload to FAISS index: ${indexName}...`
      }
    });

    // Initialize processing stages
    setProcessingStages({
      [selectedFile.name]: {
        currentStage: 'uploading',
        startTime: new Date()
      }
    });

    // Simulate progress updates for better UX
    let progress = 0;
    const progressInterval = setInterval(() => {
      progress += 5;
      if (progress <= 90) {
        setUploadStatus(prev => ({
          ...prev,
          [selectedFile.name]: {
            ...prev[selectedFile.name],
            progress,
            message: `Uploading and processing... ${progress}%`
          }
        }));

        // Update processing stage based on progress
        if (progress < 30) {
          setProcessingStages(prev => ({
            ...prev,
            [selectedFile.name]: {
              ...prev[selectedFile.name],
              currentStage: 'uploading'
            }
          }));
        } else if (progress < 60) {
          setProcessingStages(prev => ({
            ...prev,
            [selectedFile.name]: {
              ...prev[selectedFile.name],
              currentStage: 'processing'
            }
          }));
        } else {
          setProcessingStages(prev => ({
            ...prev,
            [selectedFile.name]: {
              ...prev[selectedFile.name],
              currentStage: 'indexing'
            }
          }));
        }
      }
    }, 300);

    // Upload the file to FAISS with client information, index name, and update mode
    uploadCSVToFaiss(selectedFile, selectedClient, indexName, updateMode, controller.signal, undefined, embedModel)
      .then(response => {
        clearInterval(progressInterval);

        // Log the complete data including email information and update mode
        const completeData = {
          index_name: indexName,
          email: response.client || selectedClient,
          vector_count: response.vectorCount || 0,
          embed_model: embedModel,
          update_mode: updateMode
        };

        console.log('CSV uploaded to FAISS:', JSON.stringify(completeData, null, 2));

        // Update localStorage with the correct index name
        if (typeof window !== 'undefined') {
          localStorage.setItem('faiss_index_name', indexName);
          localStorage.setItem('faiss_embed_model', embedModel);
          localStorage.setItem('faiss_client_email', selectedClient);
          console.log(`Updated credentials in localStorage: index_name=${indexName}, embed_model=${embedModel}`);
        }

        // Update processing stages
        setProcessingStages(prev => ({
          ...prev,
          [selectedFile.name]: {
            ...prev[selectedFile.name],
            currentStage: 'complete',
            endTime: new Date(),
            processingTime: ((new Date().getTime() - prev[selectedFile.name].startTime.getTime()) / 1000).toFixed(1) + ' seconds', // in seconds
            timestamp: new Date().toLocaleString()
          }
        }));

        // Update upload status
        setUploadStatus(prev => ({
          ...prev,
          [selectedFile.name]: {
            status: 'success',
            progress: 100,
            message: `Successfully uploaded to FAISS index: ${indexName}`,
            vectorCount: response.vectorCount || 0
          }
        }));
      })
      .catch(error => {
        clearInterval(progressInterval);
        if (error.name === 'AbortError') {
          console.log('Upload cancelled by user');
          return;
        }

        console.error('Error uploading CSV to FAISS:', error);

        // Update upload status
        setUploadStatus(prev => ({
          ...prev,
          [selectedFile.name]: {
            status: 'error',
            progress: 0,
            message: `Error: ${error.message}`
          }
        }));
      });
  };

  // Render upload status item
  const renderUploadStatusItem = (fileName: string, status: any) => {
    const details = processingStages[fileName] || {};

    return (
      <div key={fileName} className={`upload-status-item ${status.status}`}>
        <div className="status-header">
          <span className="file-name">{fileName}</span>
          <div className="file-status">
          {status.status === 'uploading' && (
            <div className="flex items-center gap-2">
              <span className="uploading">
                <span className="spinner"></span>
                Uploading...
              </span>
              <button
                onClick={handleStopUpload}
                className="stop-button text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 text-sm font-medium px-2 py-1 rounded-md border border-red-200 dark:border-red-800 hover:border-red-300 dark:hover:border-red-700 transition-colors"
              >
                Stop Upload
              </button>
            </div>
          )}
            {status.status === 'success' && <span className="success">Completed</span>}
            {status.status === 'error' && <span className="error">Failed</span>}
          </div>
        </div>

        {status.status === 'uploading' && (
          <div className="progress-container">
            <div className="progress-bar" style={{ width: `${status.progress}%` }}></div>
          </div>
        )}

        <div className="status-message">{status.message}</div>

        {details.currentStage && (
          <div className="processing-stages">
            {stages.map((stage, index) => (
              <div key={stage.id} className="stage-item">
                <div className={`stage-indicator ${details.currentStage === stage.id || (details.currentStage === 'complete' && stage.id !== 'complete') ? 'active' : ''} ${details.currentStage === 'complete' && stage.id === 'complete' ? 'complete' : ''}`}>
                  {details.currentStage === 'complete' && stage.id === 'complete' ? '✓' : index + 1}
                </div>
                <div className="stage-label">{stage.label}</div>
                <div className="stage-description">{stage.description}</div>
              </div>
            ))}
          </div>
        )}

        {details.currentStage === 'complete' && (
          <div className="upload-details">
            <div className="detail-item">
              <span className="detail-label">Vector Count:</span>
              <span className="detail-value">{status.vectorCount || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Processing Time:</span>
              <span className="detail-value">{details.processingTime}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Completed At:</span>
              <span className="detail-value">{details.timestamp}</span>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Handle update mode selection
  const handleUpdateModeSelect = (mode: string) => {
    setUpdateMode(mode);
    setShowUpdateModal(false);
  };

  return (
    <div className="file-upload-page flex min-h-screen">
      {/* Admin Sidebar */}
      <AdminSidebar
        currentView={currentView}
        onViewChange={setCurrentView}
        isOpen={sidebarOpen}
        onToggle={() => setSidebarOpen(!sidebarOpen)}
      />

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 lg:ml-64 overflow-auto bg-white dark:bg-gray-800">
        {/* Mobile menu button */}
        <div className="lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-primaryColor transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        <div className="max-w-4xl mx-auto p-6">
          {/* Update Mode Modal */}
          {showUpdateModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
                <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Index Already Exists</h3>
                <p className="mb-4 text-gray-600 dark:text-gray-300">
                  An index with the name <span className="font-semibold">{indexName}</span> already exists.
                  How would you like to proceed?
                </p>
                <div className="flex flex-col space-y-3">
                  <button
                    onClick={() => handleUpdateModeSelect('update')}
                    className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors"
                  >
                    Update Existing Index
                    <p className="text-xs mt-1 text-blue-100">
                      Keep existing data and add new vectors
                    </p>
                  </button>
                  <button
                    onClick={() => handleUpdateModeSelect('new')}
                    className="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md transition-colors"
                  >
                    Replace Existing Index
                    <p className="text-xs mt-1 text-red-100">
                      Delete all existing data and start fresh
                    </p>
                  </button>
                  <button
                    onClick={() => setShowUpdateModal(false)}
                    className="bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-md transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
          {/* Update Mode Modal */}
          {showUpdateModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
                <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Index Already Exists</h3>
                <p className="mb-4 text-gray-600 dark:text-gray-300">
                  An index with the name <span className="font-semibold">{indexName}</span> already exists.
                  How would you like to proceed?
                </p>
                <div className="flex flex-col space-y-3">
                  <button
                    onClick={() => handleUpdateModeSelect('update')}
                    className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors"
                  >
                    Update Existing Index
                    <p className="text-xs mt-1 text-blue-100">
                      Keep existing data and add new vectors
                    </p>
                  </button>
                  <button
                    onClick={() => handleUpdateModeSelect('new')}
                    className="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md transition-colors"
                  >
                    Replace Existing Index
                    <p className="text-xs mt-1 text-red-100">
                      Delete all existing data and start fresh
                    </p>
                  </button>
                  <button
                    onClick={() => setShowUpdateModal(false)}
                    className="bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-md transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Header Section */}
          <div className="file-upload-header text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">CSV & Excel File Upload</h1>
            <p className="text-gray-600 dark:text-gray-400 mb-4 max-w-2xl mx-auto">
              Upload a CSV or Excel file to FAISS vector database for AI-powered search and analysis
            </p>
            <div className="upload-restriction-badge bg-primaryColor text-white text-sm font-medium px-4 py-2 rounded-full inline-block">
              <span>One file at a time</span>
            </div>
          </div>

          {/* Client Selection Section */}
          <div className="client-selection mb-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
              <label htmlFor="client-dropdown" className="block font-medium mb-2 text-gray-900 dark:text-white">
                Select Email:
              </label>
              <select
                id="client-dropdown"
                value={selectedClient}
                onChange={(e) => setSelectedClient(e.target.value)}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor focus:border-primaryColor bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-colors"
                disabled={isLoadingEmails}
              >
                <option value="">-- Select Email --</option>
                {isLoadingEmails ? (
                  <option value="" disabled>Loading emails...</option>
                ) : emails.length > 0 ? (
                  emails.map((email, index) => (
                    <option key={index} value={email}>
                      {email}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>No emails available</option>
                )}
              </select>
              {emailError && <div className="text-red-500 text-sm mt-2">{emailError}</div>}
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                Select the email address to associate with this upload.
              </p>
            </div>
          </div>

          {/* Embedding Model Selection Section */}
          <div className="embedding-model-section mb-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
              <label htmlFor="embed-model" className="block font-medium mb-2 text-gray-900 dark:text-white">
                Embedding Model:
              </label>
              <select
                id="embed-model"
                value={embedModel}
                onChange={(e) => setEmbedModel(e.target.value)}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor focus:border-primaryColor bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-colors"
              >
                {Object.entries(availableModels).map(([key, model]) => (
                  <option key={key} value={key}>
                    {model.name} ({model.dimension}D) - {model.description}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                Choose the embedding model for converting text to vectors. Different models have different dimensions and capabilities.
              </p>
            </div>
          </div>

          {/* Index Name Section */}
          <div className="index-name-section mb-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
              <label htmlFor="index-name" className="block font-medium mb-2 text-gray-900 dark:text-white">
                Provide Index Name:
              </label>
              <input
                type="text"
                id="index-name"
                value={indexName}
                onChange={(e) => {
                  setIndexName(e.target.value);
                  setApiKeyError('');
                }}
                placeholder="Enter your index name (e.g., my-data-index)"
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor focus:border-primaryColor bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-colors"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                Choose a unique name for your FAISS vector index. This will be used to store and retrieve your data.
              </p>
            </div>
          </div>

          {/* File Upload Section */}
          <div className="file-upload-section mb-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
              <FileUploadWithFaiss
                onFileUpload={(files: File[]) => {
                  if (files && files.length > 0) {
                    // Store the selected file
                    setSelectedFile(files[0]);

                    // Initialize upload status
                    setUploadStatus({
                      [files[0].name]: {
                        status: 'ready',
                        progress: 0,
                        message: 'File selected, ready to upload'
                      }
                    });
                  }
                }}
                maxFileSize={50} // 50MB
                allowedTypes={['text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']}
                selectedLanguage="English"
                showFaissUpload={!!(selectedFile && selectedClient && indexName.trim())}
                indexName={indexName || 'default'}
                clientEmail={selectedClient}
                updateMode={updateMode}
                embedModel={embedModel}
                onFaissUploadSuccess={async (response) => {
                  console.log('FAISS upload successful:', response);

                  // Create PINE collection entry after successful upload
                  try {
                    await createPineCollectionEntry(embedModel, indexName, selectedClient);
                    console.log('PINE collection entry created after successful upload');
                  } catch (error) {
                    console.error('Error creating PINE collection entry after upload:', error);
                  }

                  // Update upload status
                  if (selectedFile) {
                    setUploadStatus(prev => ({
                      ...prev,
                      [selectedFile.name]: {
                        status: 'success',
                        progress: 100,
                        message: `Successfully uploaded to FAISS index: ${indexName}`,
                        vectorCount: response.vectorCount || 0
                      }
                    }));

                    // Update processing stages
                    setProcessingStages(prev => ({
                      ...prev,
                      [selectedFile.name]: {
                        ...prev[selectedFile.name],
                        currentStage: 'complete',
                        endTime: new Date(),
                        processingTime: 'Completed',
                        timestamp: new Date().toLocaleString()
                      }
                    }));
                  }

                  // Store configuration in localStorage
                  if (typeof window !== 'undefined') {
                    localStorage.setItem('faiss_index_name', indexName);
                    localStorage.setItem('faiss_embed_model', embedModel);
                    localStorage.setItem('faiss_client_email', selectedClient);

                    // Trigger a custom event to notify other components about the new upload
                    window.dispatchEvent(new CustomEvent('faissIndexUpdated', {
                      detail: { indexName, clientEmail: selectedClient, embedModel }
                    }));
                  }
                }}
                onFaissUploadError={(error) => {
                  console.error('FAISS upload error:', error);

                  // Update upload status
                  if (selectedFile) {
                    setUploadStatus(prev => ({
                      ...prev,
                      [selectedFile.name]: {
                        status: 'error',
                        progress: 0,
                        message: `Error: ${error}`
                      }
                    }));
                  }
                }}
              />

              {/* Show update mode indicator if selected */}
              {updateMode && (
                <div className={`update-mode-indicator mt-4 p-3 rounded-md ${
                  updateMode === 'update' ? 'bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' :
                  'bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800'
                }`}>
                  <div className="flex items-center">
                    <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
                      updateMode === 'update' ? 'bg-blue-500' : 'bg-red-500'
                    }`}></span>
                    <span className={`font-medium ${
                      updateMode === 'update' ? 'text-blue-700 dark:text-blue-300' : 'text-red-700 dark:text-red-300'
                    }`}>
                      {updateMode === 'update' ? 'Update Existing Index' : 'Replace Existing Index'}
                    </span>
                  </div>
                  <p className={`text-xs mt-1 ${
                    updateMode === 'update' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {updateMode === 'update'
                      ? 'New data will be added to the existing index without deleting current data.'
                      : 'All existing data will be deleted before uploading new data.'}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Upload Status */}
          {Object.keys(uploadStatus).length > 0 && (
            <div className="upload-status-container bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
              <h3 className="text-xl font-semibold mb-4 pb-2 border-b border-gray-200 dark:border-gray-600 text-gray-900 dark:text-white">
                Upload Status
              </h3>
              <div className="upload-status-list">
                {Object.entries(uploadStatus).map(([fileName, status]) =>
                  renderUploadStatusItem(fileName, status)
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileUploadPage;
