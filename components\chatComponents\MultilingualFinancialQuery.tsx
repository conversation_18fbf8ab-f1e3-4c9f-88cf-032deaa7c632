import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PiCheckCircle, PiXCircle } from 'react-icons/pi';

interface MultilingualFinancialQueryProps {
  language: 'tamil' | 'telugu' | 'kannada';
  onResponse?: (response: any) => void;
  onError?: (error: string) => void;
}

interface TranslationMetadata {
  original_query: string;
  english_query: string;
  detected_language: string;
  query_translation_successful: boolean;
  response_translated: boolean;
  translation_provider: boolean;
}

interface FinancialResponse {
  success: boolean;
  ai_response?: string;
  related_questions?: string[];
  translation_metadata?: TranslationMetadata;
  error?: string;
}

const MultilingualFinancialQuery: React.FC<MultilingualFinancialQueryProps> = ({
  language,
  onResponse,
  onError
}) => {
  const [query, setQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<FinancialResponse | null>(null);
  const [showTranslationDetails, setShowTranslationDetails] = useState(false);

  // Language-specific labels
  const labels = {
    tamil: {
      queryPlaceholder: 'உங்கள் நிதி கேள்வியை தமிழில் தட்டச்சு செய்யவும்...',
      submitButton: 'கேள்வியை அனுப்பவும்',
      submitting: 'செயலாக்குகிறது...',
      translationDetails: 'மொழிபெயர்ப்பு விவரங்கள்',
      originalQuery: 'அசல் கேள்வி',
      englishQuery: 'ஆங்கில கேள்வி',
      detectedLanguage: 'கண்டறியப்பட்ட மொழி',
      response: 'பதில்',
      relatedQuestions: 'தொடர்புடைய கேள்விகள்',
      error: 'பிழை',
      translationSuccessful: 'மொழிபெயர்ப்பு வெற்றிகரமாக',
      translationFailed: 'மொழிபெயர்ப்பு தோல்வியுற்றது',
      clear: 'துடைக்க'
    },
    telugu: {
      queryPlaceholder: 'మీ ఆర్థిక ప్రశ్నను తెలుగులో టైప్ చేయండి...',
      submitButton: 'ప్రశ్న పంపండి',
      submitting: 'ప్రాసెస్ చేస్తోంది...',
      translationDetails: 'అనువాద వివరాలు',
      originalQuery: 'అసలు ప్రశ్న',
      englishQuery: 'ఆంగ్ల ప్రశ్న',
      detectedLanguage: 'గుర్తించిన భాష',
      response: 'సమాధానం',
      relatedQuestions: 'సంబంధిత ప్రశ్నలు',
      error: 'లోపం',
      translationSuccessful: 'అనువాదం విజయవంతం',
      translationFailed: 'అనువాదం విఫలమైంది',
      clear: 'క్లియర్ చేయండి'
    },
    kannada: {
      queryPlaceholder: 'ನಿಮ್ಮ ಹಣಕಾಸಿನ ಪ್ರಶ್ನೆಯನ್ನು ಕನ್ನಡದಲ್ಲಿ ಟೈಪ್ ಮಾಡಿ...',
      submitButton: 'ಪ್ರಶ್ನೆ ಕಳುಹಿಸಿ',
      submitting: 'ಪ್ರಕ್ರಿಯೆಗೊಳಿಸುತ್ತಿದೆ...',
      translationDetails: 'ಅನುವಾದ ವಿವರಗಳು',
      originalQuery: 'ಮೂಲ ಪ್ರಶ್ನೆ',
      englishQuery: 'ಇಂಗ್ಲಿಷ್ ಪ್ರಶ್ನೆ',
      detectedLanguage: 'ಪತ್ತೆಯಾದ ಭಾಷೆ',
      response: 'ಉತ್ತರ',
      relatedQuestions: 'ಸಂಬಂಧಿತ ಪ್ರಶ್ನೆಗಳು',
      error: 'ದೋಷ',
      translationSuccessful: 'ಅನುವಾದ ಯಶಸ್ವಿ',
      translationFailed: 'ಅನುವಾದ ವಿಫಲ',
      clear: 'ಸ್ಪಷ್ಟಗೊಳಿಸಿ'
    }
  };

  const currentLabels = labels[language];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!query.trim()) return;

    setIsLoading(true);
    setResponse(null);

    try {
      console.log(`Submitting ${language} financial query:`, query);
      
      const response = await fetch('http://localhost:5010/api/multilingual_financial_query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: query.trim(),
          // Add any additional parameters you might need
          client_email: '', // Add if you have user email
          api_key: '', // Add if you have API key
          index_name: 'financial' // or whatever index you want to use
        }),
      });

      const data: FinancialResponse = await response.json();
      
      console.log('Received response:', data);
      
      setResponse(data);
      
      if (data.success) {
        onResponse?.(data);
      } else {
        onError?.(data.error || 'Unknown error occurred');
      }

    } catch (error) {
      console.error('Error submitting query:', error);
      const errorMessage = error instanceof Error ? error.message : 'Network error occurred';
      setResponse({
        success: false,
        error: errorMessage
      });
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setQuery('');
    setResponse(null);
    setShowTranslationDetails(false);
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      {/* Query Input Form */}
      <form onSubmit={handleSubmit} className="mb-6">
        <div className="mb-4">
          <textarea
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={currentLabels.queryPlaceholder}
            className="w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={4}
            disabled={isLoading}
          />
        </div>
        
        <div className="flex gap-3">
          <button
            type="submit"
            disabled={isLoading || !query.trim()}
            className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? (
              <>
                <PiSpinner className="animate-spin" />
                {currentLabels.submitting}
              </>
            ) : (
              <>
                <PiTranslate />
                {currentLabels.submitButton}
              </>
            )}
          </button>
          
          {(query || response) && (
            <button
              type="button"
              onClick={handleClear}
              className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              {currentLabels.clear}
            </button>
          )}
        </div>
      </form>

      {/* Response Display */}
      {response && (
        <div className="space-y-4">
          {/* Translation Details Toggle */}
          {response.translation_metadata && (
            <div className="mb-4">
              <button
                onClick={() => setShowTranslationDetails(!showTranslationDetails)}
                className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800"
              >
                <PiTranslate />
                {currentLabels.translationDetails}
                {response.translation_metadata.query_translation_successful ? (
                  <PiCheckCircle className="text-green-500" />
                ) : (
                  <PiXCircle className="text-red-500" />
                )}
              </button>
              
              {showTranslationDetails && (
                <div className="mt-2 p-4 bg-gray-50 rounded-lg text-sm space-y-2">
                  <div>
                    <strong>{currentLabels.originalQuery}:</strong> {response.translation_metadata.original_query}
                  </div>
                  <div>
                    <strong>{currentLabels.englishQuery}:</strong> {response.translation_metadata.english_query}
                  </div>
                  <div>
                    <strong>{currentLabels.detectedLanguage}:</strong> {response.translation_metadata.detected_language}
                  </div>
                  <div className="flex items-center gap-2">
                    <strong>Status:</strong>
                    {response.translation_metadata.query_translation_successful ? (
                      <span className="text-green-600">{currentLabels.translationSuccessful}</span>
                    ) : (
                      <span className="text-red-600">{currentLabels.translationFailed}</span>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Success Response */}
          {response.success && response.ai_response && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-2">{currentLabels.response}</h3>
              <div className="text-green-700 whitespace-pre-wrap">{response.ai_response}</div>
              
              {/* Related Questions */}
              {response.related_questions && response.related_questions.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-medium text-green-800 mb-2">{currentLabels.relatedQuestions}</h4>
                  <ul className="list-disc list-inside space-y-1 text-green-700">
                    {response.related_questions.map((question, index) => (
                      <li key={index} className="cursor-pointer hover:text-green-900" onClick={() => setQuery(question)}>
                        {question}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Error Response */}
          {!response.success && response.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="font-semibold text-red-800 mb-2">{currentLabels.error}</h3>
              <div className="text-red-700">{response.error}</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MultilingualFinancialQuery;
