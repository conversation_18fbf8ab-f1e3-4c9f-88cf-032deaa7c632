import React, { useState, useEffect, memo } from 'react';
import Image from 'next/image';
import { PiGlobe, PiArrowSquareOut, PiSpinner, PiLink, PiInfo } from 'react-icons/pi';

interface WebpagePreviewProps {
  url: string;
  summary?: string;
  referenceNumber: number;
}

// Using memo to prevent unnecessary re-renders
const WebpagePreview: React.FC<WebpagePreviewProps> = memo(({
  url,
  summary,
  referenceNumber
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [faviconUrl, setFaviconUrl] = useState<string | null>(null);
  const [domainName, setDomainName] = useState<string>('');

  // Extract domain name for display
  useEffect(() => {
    try {
      const domain = new URL(url).hostname;
      setDomainName(domain.replace('www.', ''));
    } catch (error) {
      setDomainName(url);
    }
  }, [url]);

  // Get favicon URL
  useEffect(() => {
    try {
      const domain = new URL(url).origin;
      setFaviconUrl(`${domain}/favicon.ico`);
      // Set loading to false after a short delay to avoid flickering
      const timer = setTimeout(() => setIsLoading(false), 300);
      return () => clearTimeout(timer);
    } catch (error) {
      setFaviconUrl(null);
      setIsLoading(false);
    }
  }, [url]);

  // Function to handle explicit visit button click
  const handleVisitClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div
      className="p-3 bg-white dark:bg-gray-800 shadow-xl rounded-lg border border-primaryColor/20 z-50 w-80"
      // Using onMouseDown instead of onClick for better performance
      onMouseDown={(e) => e.stopPropagation()}
    >
      <div className="flex items-center gap-2 mb-2">
        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-primaryColor/20 to-blue-500/20 rounded-full overflow-hidden">
          {faviconUrl ? (
            <Image
              src={faviconUrl}
              alt={domainName}
              width={16}
              height={16}
              onError={() => setFaviconUrl(null)}
              className="w-4 h-4"
              unoptimized // Skip image optimization for better performance
            />
          ) : (
            <PiGlobe className="text-primaryColor" />
          )}
        </div>
        <div className="flex-1 truncate">
          <p className="text-sm font-medium text-n700 dark:text-n30 truncate">
            {domainName}
          </p>
        </div>
        <span className="text-xs font-medium text-white bg-gradient-to-r from-primaryColor to-blue-600 px-2 py-0.5 rounded-full shadow-sm">
          {referenceNumber}
        </span>
      </div>

      {/* URL display */}
      <div className="flex items-center text-xs text-n500 dark:text-n400 mb-2 bg-gray-50 dark:bg-gray-700/50 p-1.5 rounded">
        <PiLink className="mr-1 text-primaryColor flex-shrink-0" />
        <span className="truncate">
          {url}
        </span>
      </div>

      {/* Summary section - more prominent */}
      {summary && (
        <div className="bg-gray-50 dark:bg-gray-700/50 p-2 rounded mb-2">
          <div className="flex items-start">
            <PiInfo className="text-primaryColor mr-1 mt-0.5 flex-shrink-0" />
            <p className="text-xs text-n600 dark:text-n300 line-clamp-4">
              {summary}
            </p>
          </div>
        </div>
      )}

      {/* Preview section - smaller */}
      <div className="relative w-full h-24 mb-2 bg-gray-100 dark:bg-gray-700 rounded overflow-hidden shadow-inner">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div className="animate-pulse flex flex-col items-center">
              <div className="w-8 h-8 bg-primaryColor/20 rounded-full mb-1 flex items-center justify-center">
                <PiSpinner className="text-primaryColor animate-spin" />
              </div>
              <div className="h-1.5 w-20 bg-primaryColor/20 rounded"></div>
            </div>
          </div>
        )}

        {/* We use an img tag instead of iframe for better security and performance */}
        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-b from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
          <p className="text-xs text-n500 dark:text-n400 px-3 text-center">
            Reference: {domainName}
          </p>
        </div>
      </div>

      <div className="flex items-center justify-end text-xs">
        <button
          className="flex items-center gap-1 text-white bg-gradient-to-r from-primaryColor to-blue-600 px-2 py-1 rounded shadow-sm hover:shadow-md transition-all duration-200"
          onClick={handleVisitClick}
        >
          <span>Visit Source</span>
          <PiArrowSquareOut />
        </button>
      </div>
    </div>
  );
});

// Add display name for debugging
WebpagePreview.displayName = 'WebpagePreview';

export default WebpagePreview;
