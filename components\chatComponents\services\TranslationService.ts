export class TranslationService {
  private static translationCache = new Map<string, string>();

  // Language detection helper
  static detectLanguage(text: string): string {
    if (!text || !text.trim()) return 'en';

    // Tamil detection using Unicode ranges
    if (/[\u0B80-\u0BFF]/.test(text)) return 'ta';

    // Telugu detection using Unicode ranges
    if (/[\u0C00-\u0C7F]/.test(text)) return 'te';

    // Kannada detection using Unicode ranges
    if (/[\u0C80-\u0CFF]/.test(text)) return 'kn';

    // Hindi detection using Unicode ranges
    if (/[\u0900-\u097F]/.test(text)) return 'hi';

    // Arabic detection
    if (/[\u0600-\u06FF]/.test(text)) return 'ar';

    // Chinese detection
    if (/[\u4e00-\u9fff]/.test(text)) return 'zh';

    // Default to English
    return 'en';
  }

  // Fast translation function with timeout for better performance
  static async translateText(text: string, sourceLang: string, targetLang: string): Promise<string> {
    console.log(`🚀 Fast translating from ${sourceLang} to ${targetLang}: ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`);

    // If source and target languages are the same, return original text immediately
    if (sourceLang === targetLang) {
      console.log("⚠️ Source and target languages are the same, returning original text");
      return text;
    }

    // Check cache first for instant response
    const cacheKey = `${sourceLang}-${targetLang}-${text}`;
    const cachedTranslation = this.translationCache.get(cacheKey);
    if (cachedTranslation) {
      console.log("💾 Using cached translation (instant)");
      return cachedTranslation;
    }

    try {
      console.log("🚀 Using fast direct translation service with timeout");

      // Create a timeout promise for faster responses
      const timeoutPromise = new Promise<string>((_, reject) => {
        setTimeout(() => reject(new Error('Translation timeout')), 5000); // 5 second timeout
      });

      // Try direct MyMemory API first (faster than backend)
      const translationPromise = this.translateWithMyMemory(text, sourceLang, targetLang);

      const directTranslation = await Promise.race([translationPromise, timeoutPromise]);
      if (directTranslation && directTranslation !== text) {
        console.log(`✅ Direct translation successful: ${directTranslation.substring(0, 50)}${directTranslation.length > 50 ? '...' : ''}`);
        this.translationCache.set(cacheKey, directTranslation);
        return directTranslation;
      }

      // If direct translation fails, try backend as fallback
      console.log("🔄 Direct translation failed, trying backend service");
      const response = await fetch('http://localhost:5010/api/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: text,
          source_lang: sourceLang,
          target_lang: targetLang
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data && data.data.translated_text) {
          const translatedText = data.data.translated_text;
          console.log(`✅ Backend translation successful: ${translatedText.substring(0, 50)}${translatedText.length > 50 ? '...' : ''}`);
          this.translationCache.set(cacheKey, translatedText);
          return translatedText;
        }
      }

      // If both fail, try fallback patterns
      console.log("🔄 All translation services failed, trying fallback patterns");
      const fallbackTranslation = this.getFallbackTranslation(text, sourceLang, targetLang);

      if (fallbackTranslation !== text) {
        console.log(`✅ Fallback translation successful: ${fallbackTranslation.substring(0, 50)}${fallbackTranslation.length > 50 ? '...' : ''}`);
        this.translationCache.set(cacheKey, fallbackTranslation);
        return fallbackTranslation;
      }

    } catch (error) {
      console.error("❌ Translation error:", error);
    }
    
    // Final fallback - return original text
    console.log("🔄 All translation methods failed, returning original text");
    this.translationCache.set(cacheKey, text);
    return text;
  }

  // Fallback translation using pattern matching
  static getFallbackTranslation(text: string, sourceLang: string, targetLang: string): string {
    // Common translation patterns for basic phrases
    const translationPatterns: Record<string, Record<string, string>> = {
      'en-ta': {
        'Hello': 'வணக்கம்',
        'Thank you': 'நன்றி',
        'Yes': 'ஆம்',
        'No': 'இல்லை',
        'Please': 'தயவுசெய்து',
        'Sorry': 'மன்னிக்கவும்',
        'Good morning': 'காலை வணக்கம்',
        'Good evening': 'மாலை வணக்கம்',
        'How are you?': 'நீங்கள் எப்படி இருக்கிறீர்கள்?',
        'What is your name?': 'உங்கள் பெயர் என்ன?'
      },
      'en-te': {
        'Hello': 'హలో',
        'Thank you': 'ధన్యవాదాలు',
        'Yes': 'అవును',
        'No': 'లేదు',
        'Please': 'దయచేసి',
        'Sorry': 'క్షమించండి',
        'Good morning': 'శుభోదయం',
        'Good evening': 'శుభ సాయంత్రం',
        'How are you?': 'మీరు ఎలా ఉన్నారు?',
        'What is your name?': 'మీ పేరు ఏమిటి?'
      },
      'en-kn': {
        'Hello': 'ಹಲೋ',
        'Thank you': 'ಧನ್ಯವಾದಗಳು',
        'Yes': 'ಹೌದು',
        'No': 'ಇಲ್ಲ',
        'Please': 'ದಯವಿಟ್ಟು',
        'Sorry': 'ಕ್ಷಮಿಸಿ',
        'Good morning': 'ಶುಭೋದಯ',
        'Good evening': 'ಶುಭ ಸಂಜೆ',
        'How are you?': 'ನೀವು ಹೇಗಿದ್ದೀರಿ?',
        'What is your name?': 'ನಿಮ್ಮ ಹೆಸರು ಏನು?'
      },
      'ta-en': {
        'வணக்கம்': 'Hello',
        'நன்றி': 'Thank you',
        'ஆம்': 'Yes',
        'இல்லை': 'No',
        'தயவுசெய்து': 'Please',
        'மன்னிக்கவும்': 'Sorry',
        'காலை வணக்கம்': 'Good morning',
        'மாலை வணக்கம்': 'Good evening'
      },
      'te-en': {
        'హలో': 'Hello',
        'ధన్యవాదాలు': 'Thank you',
        'అవును': 'Yes',
        'లేదు': 'No',
        'దయచేసి': 'Please',
        'క్షమించండి': 'Sorry',
        'శుభోదయం': 'Good morning',
        'శుభ సాయంత్రం': 'Good evening'
      },
      'kn-en': {
        'ಹಲೋ': 'Hello',
        'ಧನ್ಯವಾದಗಳು': 'Thank you',
        'ಹೌದು': 'Yes',
        'ಇಲ್ಲ': 'No',
        'ದಯವಿಟ್ಟು': 'Please',
        'ಕ್ಷಮಿಸಿ': 'Sorry',
        'ಶುಭೋದಯ': 'Good morning',
        'ಶುಭ ಸಂಜೆ': 'Good evening'
      }
    };

    const patternKey = `${sourceLang}-${targetLang}`;
    const patterns = translationPatterns[patternKey];
    
    if (patterns) {
      // Try exact match first
      if (patterns[text]) {
        return patterns[text];
      }
      
      // Try partial matches for longer text
      let translatedText = text;
      for (const [source, target] of Object.entries(patterns)) {
        if (text.includes(source)) {
          translatedText = translatedText.replace(source, target);
        }
      }
      
      if (translatedText !== text) {
        return translatedText;
      }
    }

    return text; // Return original if no translation found
  }

  // Function to extract and preserve capital words during translation
  static extractCapitalWords(text: string): { text: string; capitalWords: Array<{ word: string; placeholder: string }> } {
    const capitalWordsMatches = text.match(/\b[A-Z]{2,}\b/g) || [];
    const capitalWords = capitalWordsMatches.map((word: string) => ({
      word,
      placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`
    }));

    let textWithPlaceholders = text;
    capitalWords.forEach((item) => {
      textWithPlaceholders = textWithPlaceholders.replace(item.word, item.placeholder);
    });

    return { text: textWithPlaceholders, capitalWords };
  }

  // Function to restore capital words after translation
  static restoreCapitalWords(text: string, capitalWords: Array<{ word: string; placeholder: string }>): string {
    let restoredText = text;
    capitalWords.forEach((item) => {
      restoredText = restoredText.replace(item.placeholder, item.word);
    });
    return restoredText;
  }

  // Function to translate text while preserving capital words
  static async translateWithCapitalWordsPreservation(
    text: string,
    sourceLang: string,
    targetLang: string
  ): Promise<string> {
    const { text: textWithPlaceholders, capitalWords } = this.extractCapitalWords(text);
    const translatedText = await this.translateText(textWithPlaceholders, sourceLang, targetLang);
    return this.restoreCapitalWords(translatedText, capitalWords);
  }

  // Function to translate entire response objects
  static async translateResponse(response: any, targetLang: string): Promise<any> {
    if (!response || !targetLang) return response;

    const translatedResponse = { ...response };

    try {
      // Detect source language from AI response
      const sourceLang = response.ai_response ? this.detectLanguage(response.ai_response) : 'en';

      // Skip translation if source and target are the same
      if (sourceLang === targetLang) {
        console.log(`⚠️ Source and target languages are the same (${targetLang}), skipping translation`);
        return response;
      }

      console.log(`🌐 Translating response from ${sourceLang} to ${targetLang}`);

      // Translate AI response
      if (response.ai_response) {
        translatedResponse.ai_response = await this.translateWithCapitalWordsPreservation(
          response.ai_response,
          sourceLang,
          targetLang
        );
      }

      // Translate related questions
      if (response.related_questions && Array.isArray(response.related_questions)) {
        translatedResponse.related_questions = await Promise.all(
          response.related_questions.map((question: string) =>
            this.translateWithCapitalWordsPreservation(question, sourceLang, targetLang)
          )
        );
      }

      // Add translation metadata
      translatedResponse.translation_applied = true;
      translatedResponse.source_language = sourceLang;
      translatedResponse.target_language = targetLang;
      translatedResponse.translation_timestamp = new Date().toISOString();

      console.log(`✅ Response translation completed: ${sourceLang} -> ${targetLang}`);
      return translatedResponse;

    } catch (error) {
      console.error('❌ Error translating response:', error);
      // Return original response with error metadata
      return {
        ...response,
        translation_applied: false,
        translation_error: error instanceof Error ? error.message : 'Unknown translation error'
      };
    }
  }

  // Function to get language name from code
  static getLanguageName(langCode: string): string {
    const languageNames: Record<string, string> = {
      'en': 'English',
      'ta': 'Tamil',
      'te': 'Telugu',
      'kn': 'Kannada',
      'hi': 'Hindi',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'zh': 'Chinese',
      'ja': 'Japanese',
      'ko': 'Korean',
      'ar': 'Arabic'
    };

    return languageNames[langCode] || langCode;
  }

  // Function to clear translation cache
  static clearCache(): void {
    this.translationCache.clear();
    console.log('🗑️ Translation cache cleared');
  }

  // Function to get cache statistics
  static getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.translationCache.size,
      keys: Array.from(this.translationCache.keys()).slice(0, 10) // Show first 10 keys
    };
  }

  // Fast direct translation using MyMemory API
  static async translateWithMyMemory(text: string, sourceLang: string, targetLang: string): Promise<string | null> {
    try {
      console.log(`🌐 Attempting direct MyMemory translation: ${sourceLang} -> ${targetLang}`);

      const url = "https://api.mymemory.translated.net/get";
      const params = new URLSearchParams({
        q: text,
        langpair: `${sourceLang}|${targetLang}`
      });

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: AbortSignal.timeout(4000) // 4 second timeout for the API call
      });

      if (response.ok) {
        const data = await response.json();
        if (data.responseStatus === 200) {
          const translatedText = data.responseData?.translatedText;
          if (translatedText && translatedText !== text) {
            console.log(`✅ MyMemory translation successful: ${translatedText.substring(0, 50)}${translatedText.length > 50 ? '...' : ''}`);
            return translatedText;
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️ MyMemory translation failed:`, error);
    }

    return null;
  }
}
