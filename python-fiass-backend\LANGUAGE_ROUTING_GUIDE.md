# Language Detection and Query Routing System

This document describes the comprehensive language detection and query routing system implemented for handling Tamil, Telugu, and Kannada languages with direct retrieval and cross-language translation flows.

## Overview

The system implements intelligent language detection and routing that:

1. **Detects language** from user queries and CSV data with high accuracy
2. **Routes Tamil queries directly** to the Tamil FAISS API for native processing
3. **Handles cross-language scenarios** with translation flows
4. **Stores language metadata** for proper query routing

## Architecture

### Core Components

1. **Enhanced Language Detection Service** (`services/language_utils.py`)
   - Advanced Unicode-based detection for South Indian languages
   - Confidence scoring and priority weighting
   - CSV/DataFrame language detection

2. **Tamil Query Router** (`services/tamil_query_router.py`)
   - Routes Tamil queries directly to Tamil FAISS API
   - Bypasses translation for native Tamil processing
   - Health checking and error handling

3. **Cross-Language Processor** (`services/cross_language_processor.py`)
   - Handles translation flows for different language combinations
   - Query translation to data language
   - Response translation back to user language

4. **Main Query Handler** (enhanced in `full_code.py`)
   - Integrated language detection and routing logic
   - Fallback mechanisms and error handling

## Language Detection Flow

### 1. Query Language Detection

```python
# Enhanced detection with confidence scoring
language, confidence, scores = enhanced_language_detector.detect_language_with_confidence(query)

# Priority languages: Tamil, Telugu, Kannada
if confidence >= 0.1 and language in PRIORITY_LANGUAGES:
    # Use detected language
else:
    # Fallback to basic detection or English
```

### 2. CSV/Excel Data Language Detection

```python
# Detect language from uploaded data
detected_language, confidence, metadata = enhanced_language_detector.detect_csv_language(dataframe)

# Store language metadata with vectors
for meta_item in metadata:
    meta_item['detected_language'] = detected_language
    meta_item['language_confidence'] = confidence
```

## Query Routing Logic

### Tamil Query Direct Routing

When a Tamil query is detected:

1. **Check routing conditions**:
   ```python
   if query_language == 'Tamil' and confidence >= 0.1:
       should_route = tamil_router.should_route_to_tamil_api(query)
   ```

2. **Route to Tamil API**:
   ```python
   if should_route:
       response = tamil_router.route_tamil_query(query, additional_params)
       return response  # Direct Tamil processing
   ```

3. **Fallback to standard processing** if Tamil API fails

### Cross-Language Translation Flow

For queries where language differs from data language:

1. **Detect need for translation**:
   ```python
   should_translate = cross_language_processor.should_use_translation_flow(
       query_language, data_language, user_requested_language
   )
   ```

2. **Translation flow**:
   - Translate query to data language
   - Perform search with translated query
   - Translate response back to user's language

## Index Routing

### Language-Specific Index Selection

```python
# Automatic index routing based on detected language
if detected_language == 'Tamil':
    index_name = 'default'  # Tamil uses default index
elif detected_language == 'Telugu':
    index_name = 'default-telugu'
elif detected_language == 'Kannada':
    index_name = 'default-kannada'
else:
    index_name = f"default-{language.lower()}"
```

## Usage Examples

### 1. Tamil Query (Direct Routing)

```python
# Input: Tamil query
query = "மின்சாரம் வழங்க வேண்டும்"

# Process:
# 1. Detect language: Tamil (confidence: 0.95)
# 2. Route to Tamil API directly
# 3. Return Tamil response without translation

# Output: Native Tamil response
{
    "query": "மின்சாரம் வழங்க வேண்டும்",
    "ai_response": "மின்சாரம் வழங்குவதற்கான வழிகள்...",
    "language_processing": "direct_tamil_api"
}
```

### 2. Cross-Language Query (Translation Flow)

```python
# Input: English query on Tamil data
query = "I need electricity supply"
data_language = "Tamil"

# Process:
# 1. Detect query language: English
# 2. Detect data language: Tamil
# 3. Translate query to Tamil
# 4. Search Tamil data
# 5. Translate response back to English

# Output: Translated response
{
    "query": "I need electricity supply",
    "translated_query": "மின்சாரம் வழங்க வேண்டும்",
    "ai_response": "Ways to provide electricity supply...",
    "cross_language_processing": {
        "applied": true,
        "query_language": "English",
        "data_language": "Tamil"
    }
}
```

### 3. CSV Upload with Language Detection

```python
# Upload Tamil CSV data
# Process:
# 1. Detect CSV language: Tamil (confidence: 0.87)
# 2. Route to Tamil index: 'default'
# 3. Store language metadata with vectors

# Response includes language information
{
    "detected_language": "Tamil",
    "language_confidence": 0.87,
    "index_routing": {
        "original_index": "default",
        "final_index": "default",
        "routing_applied": false
    }
}
```

## Configuration

### Language Priority Settings

```python
# Priority languages for South Indian language detection
PRIORITY_LANGUAGES = ['Tamil', 'Telugu', 'Kannada']

# Language code mappings
LANGUAGE_CODE_MAP = {
    'Tamil': 'ta',
    'Telugu': 'te', 
    'Kannada': 'kn',
    'English': 'en'
}
```

### Detection Thresholds

```python
# Confidence threshold for language detection
confidence_threshold = 0.1

# Minimum characters needed for detection
min_chars_threshold = 5
```

## Testing

### Running Tests

```bash
# Run comprehensive test suite
python run_language_tests.py

# Run specific unit tests
python tests/test_language_routing.py
```

### Test Coverage

- ✅ Language detection accuracy
- ✅ Tamil query routing
- ✅ Cross-language translation flows
- ✅ CSV language detection
- ✅ Index routing logic
- ✅ Error handling and fallbacks

## Error Handling

### Fallback Mechanisms

1. **Tamil API unavailable**: Falls back to standard processing
2. **Translation service unavailable**: Uses original language
3. **Language detection fails**: Defaults to English
4. **Index not found**: Uses default index

### Error Response Format

```python
{
    "error": "Error description",
    "error_type": "error_category",
    "routing_info": {
        "routed_to_tamil_api": true,
        "processing_type": "direct_tamil_retrieval",
        "error_details": "Detailed error information"
    }
}
```

## Performance Considerations

- **Language detection**: ~10ms for typical queries
- **Tamil API routing**: ~100-500ms depending on network
- **Translation**: ~200-1000ms per translation
- **Caching**: Translation results cached for performance

## Future Enhancements

1. **Additional Languages**: Support for more Indian languages
2. **Improved Detection**: Machine learning-based detection
3. **Caching**: Enhanced caching for translations
4. **Analytics**: Language usage analytics and insights

## Troubleshooting

### Common Issues

1. **Tamil API not responding**:
   - Check if Tamil FAISS service is running
   - Verify network connectivity
   - Check logs for connection errors

2. **Translation not working**:
   - Verify translation service is available
   - Check API keys and configuration
   - Review language code mappings

3. **Language detection inaccurate**:
   - Check confidence thresholds
   - Verify Unicode character ranges
   - Review priority language settings
