#!/usr/bin/env python3
"""
Tamil Language Routing Demonstration

This script demonstrates the Tamil language detection and routing system
with real examples showing how queries are processed.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_language_detection():
    """Demonstrate language detection capabilities."""
    print("🔍 LANGUAGE DETECTION DEMONSTRATION")
    print("=" * 60)
    
    try:
        from services.language_utils import enhanced_language_detector
        
        # Test queries in different languages
        test_queries = [
            # Tamil queries
            "மின்சாரம் வழங்க வேண்டும்",
            "விவசாய கடன் தேவை",
            "வங்கி கணக்கு திறக்க வேண்டும்",
            
            # Telugu queries  
            "విద్యుత్ సరఫరా అవసరం",
            "వ్యవసాయ రుణం కావాలి",
            
            # Kannada queries
            "ವಿದ್ಯುತ್ ಪೂರೈಕೆ ಅವಶ್ಯಕ",
            "ಕೃಷಿ ಸಾಲ ಬೇಕು",
            
            # English queries
            "I need electricity supply",
            "Import of wheat with poisoning",
            "Agricultural loan requirements",
            
            # Mixed language
            "Hello மின்சாரம் world"
        ]
        
        for query in test_queries:
            language, confidence, scores = enhanced_language_detector.detect_language_with_confidence(query)
            
            # Color coding for different languages
            if language == 'Tamil':
                color = "🟢"
            elif language == 'Telugu':
                color = "🔵"
            elif language == 'Kannada':
                color = "🟡"
            elif language == 'English':
                color = "⚪"
            else:
                color = "🟣"
            
            print(f"{color} {language:8} (conf: {confidence:.3f}) | {query}")
            
            # Show top language scores if multiple detected
            if len(scores) > 1:
                top_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:2]
                print(f"   📊 Top scores: {top_scores}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Enhanced language detector not available: {e}")
        return False

def demo_tamil_routing():
    """Demonstrate Tamil query routing logic."""
    print("\n🌏 TAMIL QUERY ROUTING DEMONSTRATION")
    print("=" * 60)
    
    try:
        from services.tamil_query_router import tamil_router
        
        # Test queries for routing decisions
        routing_tests = [
            # Tamil queries - should route to Tamil API
            ("மின்சாரம் வழங்க வேண்டும்", "Tamil financial query"),
            ("விவசாய கடன் தேவை", "Tamil agricultural loan query"),
            ("வங்கி கணக்கு திறக்க வேண்டும்", "Tamil banking query"),
            
            # Non-Tamil queries - should use standard processing
            ("I need electricity supply", "English query"),
            ("Import of wheat with poisoning", "English query"),
            ("విద్యుత్ సరఫరా అవసరం", "Telugu query"),
            ("ವಿದ್ಯುತ್ ಪೂರೈಕೆ ಅವಶ್ಯಕ", "Kannada query"),
        ]
        
        for query, description in routing_tests:
            should_route = tamil_router.should_route_to_tamil_api(query)
            
            if should_route:
                print(f"🌏 ROUTE TO TAMIL API: {query}")
                print(f"   📝 {description}")
                print(f"   ✅ Will use Tamil FAISS API for native processing")
            else:
                print(f"🔄 STANDARD PROCESSING: {query}")
                print(f"   📝 {description}")
                print(f"   ⚙️ Will use standard translation + search pipeline")
            print()
        
        return True
        
    except ImportError as e:
        print(f"❌ Tamil router not available: {e}")
        return False

def demo_cross_language_scenarios():
    """Demonstrate cross-language processing scenarios."""
    print("\n🌐 CROSS-LANGUAGE PROCESSING SCENARIOS")
    print("=" * 60)
    
    try:
        from services.cross_language_processor import cross_language_processor
        
        # Different language combination scenarios
        scenarios = [
            {
                "query": "மின்சாரம் வழங்க வேண்டும்",
                "query_lang": "Tamil",
                "data_lang": "Tamil",
                "user_lang": None,
                "description": "Tamil query on Tamil data"
            },
            {
                "query": "I need electricity supply",
                "query_lang": "English", 
                "data_lang": "Tamil",
                "user_lang": None,
                "description": "English query on Tamil data"
            },
            {
                "query": "மின்சாரம் வழங்க வேண்டும்",
                "query_lang": "Tamil",
                "data_lang": "English",
                "user_lang": None,
                "description": "Tamil query on English data"
            },
            {
                "query": "மின்சாரம் வழங்க வேண்டும்",
                "query_lang": "Tamil",
                "data_lang": "Tamil",
                "user_lang": "English",
                "description": "Tamil query, Tamil data, English response requested"
            }
        ]
        
        for scenario in scenarios:
            should_translate = cross_language_processor.should_use_translation_flow(
                scenario["query_lang"], 
                scenario["data_lang"], 
                scenario["user_lang"]
            )
            
            print(f"📋 SCENARIO: {scenario['description']}")
            print(f"   🔤 Query: {scenario['query']}")
            print(f"   📊 Query Language: {scenario['query_lang']}")
            print(f"   💾 Data Language: {scenario['data_lang']}")
            print(f"   👤 User Requested: {scenario['user_lang'] or 'Same as query'}")
            
            if should_translate:
                print(f"   🌐 TRANSLATION FLOW: Required")
                if scenario['query_lang'] != scenario['data_lang']:
                    print(f"      1️⃣ Translate query: {scenario['query_lang']} → {scenario['data_lang']}")
                    print(f"      2️⃣ Search in {scenario['data_lang']} data")
                if scenario['user_lang'] and scenario['user_lang'] != scenario['data_lang']:
                    print(f"      3️⃣ Translate response: {scenario['data_lang']} → {scenario['user_lang']}")
            else:
                print(f"   ✅ DIRECT PROCESSING: No translation needed")
            print()
        
        return True
        
    except ImportError as e:
        print(f"❌ Cross-language processor not available: {e}")
        return False

def demo_api_health():
    """Check Tamil API health status."""
    print("\n🏥 TAMIL API HEALTH CHECK")
    print("=" * 60)
    
    try:
        from services.tamil_query_router import tamil_router
        
        health = tamil_router.check_tamil_api_health()
        
        if health.get('healthy'):
            print(f"✅ Tamil API is HEALTHY")
            print(f"   🌐 Endpoint: {health.get('endpoint')}")
            print(f"   📊 Status Code: {health.get('status_code')}")
            print(f"   💡 Ready to handle Tamil queries")
        else:
            print(f"❌ Tamil API is NOT HEALTHY")
            print(f"   🌐 Endpoint: {health.get('endpoint')}")
            print(f"   ⚠️ Error: {health.get('error')}")
            print(f"   💡 Tamil queries will fall back to standard processing")
        
        return health.get('healthy', False)
        
    except ImportError as e:
        print(f"❌ Tamil router not available: {e}")
        return False

def main():
    """Main demonstration function."""
    print("🎭 TAMIL LANGUAGE ROUTING SYSTEM DEMONSTRATION")
    print("=" * 70)
    print("This demo shows how the system handles different language scenarios")
    print("=" * 70)
    
    # Run all demonstrations
    demos = [
        ("Language Detection", demo_language_detection),
        ("Tamil Query Routing", demo_tamil_routing),
        ("Cross-Language Scenarios", demo_cross_language_scenarios),
        ("Tamil API Health", demo_api_health)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        try:
            result = demo_func()
            results.append((demo_name, result))
        except Exception as e:
            print(f"❌ Error in {demo_name}: {e}")
            results.append((demo_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 DEMONSTRATION SUMMARY")
    print("=" * 70)
    
    for demo_name, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{status} - {demo_name}")
    
    print("\n💡 KEY TAKEAWAYS:")
    print("• Tamil queries are automatically detected and routed to Tamil API")
    print("• Non-Tamil queries use standard processing with translation")
    print("• Cross-language scenarios are handled with appropriate translation flows")
    print("• System gracefully falls back if Tamil API is unavailable")
    print("• Language detection works for Tamil, Telugu, Kannada, and English")
    
    print("\n🚀 SYSTEM STATUS:")
    tamil_api_healthy = any(result for name, result in results if name == "Tamil API Health")
    if tamil_api_healthy:
        print("✅ Tamil API is available - Full Tamil routing functionality active")
    else:
        print("⚠️ Tamil API unavailable - Using fallback processing for Tamil queries")

if __name__ == '__main__':
    main()
