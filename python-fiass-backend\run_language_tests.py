#!/usr/bin/env python3
"""
Language Routing Test Runner

This script runs comprehensive tests for the language detection and routing system.
It validates Tamil query routing, cross-language translation flows, and edge cases.
"""

import os
import sys
import subprocess
import json
from datetime import datetime

def print_header():
    """Print test header."""
    print("=" * 70)
    print("🧪 LANGUAGE ROUTING SYSTEM TEST SUITE")
    print("=" * 70)
    print(f"📅 Test run started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def check_dependencies():
    """Check if required dependencies are available."""
    print("🔍 Checking dependencies...")
    
    dependencies = [
        'services.language_utils',
        'services.tamil_query_router', 
        'services.cross_language_processor',
        'services.translation_service'
    ]
    
    available = []
    missing = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            available.append(dep)
            print(f"  ✅ {dep}")
        except ImportError:
            missing.append(dep)
            print(f"  ❌ {dep}")
    
    print(f"\n📊 Dependencies: {len(available)}/{len(dependencies)} available")
    
    if missing:
        print(f"⚠️  Missing dependencies: {', '.join(missing)}")
        print("   Some tests may be skipped.")
    
    print()
    return len(available) > 0

def run_unit_tests():
    """Run the unit tests."""
    print("🧪 Running unit tests...")
    
    test_file = os.path.join(os.path.dirname(__file__), 'tests', 'test_language_routing.py')
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    try:
        # Run the test file
        result = subprocess.run([
            sys.executable, test_file
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        print("📋 Test Output:")
        print("-" * 50)
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  Test Errors:")
            print("-" * 50)
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n🎯 Unit Tests: {'PASSED' if success else 'FAILED'}")
        return success
        
    except Exception as e:
        print(f"❌ Error running tests: {str(e)}")
        return False

def test_manual_language_detection():
    """Run manual language detection tests."""
    print("🔍 Running manual language detection tests...")
    
    test_cases = [
        ("மின்சாரம் வழங்க வேண்டும்", "Tamil"),
        ("విద్యుత్ సరఫరా అవసరం", "Telugu"), 
        ("ವಿದ್ಯುತ್ ಪೂರೈಕೆ ಅವಶ್ಯಕ", "Kannada"),
        ("I need electricity supply", "English"),
        ("Hello மின்சாரம் world", "Tamil"),  # Mixed text
    ]
    
    try:
        from services.language_utils import enhanced_language_detector
        
        results = []
        for text, expected in test_cases:
            language, confidence, _ = enhanced_language_detector.detect_language_with_confidence(text)
            success = language == expected or (expected == "Tamil" and language in ["Tamil", "English"])
            results.append({
                'text': text[:30] + "..." if len(text) > 30 else text,
                'expected': expected,
                'detected': language,
                'confidence': confidence,
                'success': success
            })
            
            status = "✅" if success else "❌"
            print(f"  {status} '{text[:30]}...' -> {language} (confidence: {confidence:.3f})")
        
        success_count = sum(1 for r in results if r['success'])
        print(f"\n📊 Manual Detection Tests: {success_count}/{len(results)} passed")
        return success_count == len(results)
        
    except ImportError:
        print("  ⚠️  Enhanced language detector not available, skipping manual tests")
        return True

def test_tamil_routing_logic():
    """Test Tamil routing logic."""
    print("🌏 Testing Tamil routing logic...")
    
    try:
        from services.tamil_query_router import tamil_router
        
        test_cases = [
            ("மின்சாரம் வழங்க வேண்டும்", True),
            ("I need electricity", False),
            ("Hello world", False),
        ]
        
        results = []
        for query, expected in test_cases:
            should_route = tamil_router.should_route_to_tamil_api(query)
            success = should_route == expected
            results.append(success)
            
            status = "✅" if success else "❌"
            print(f"  {status} '{query}' -> Route: {should_route} (expected: {expected})")
        
        success_count = sum(results)
        print(f"\n📊 Tamil Routing Tests: {success_count}/{len(results)} passed")
        return success_count == len(results)
        
    except ImportError:
        print("  ⚠️  Tamil router not available, skipping routing tests")
        return True

def test_cross_language_logic():
    """Test cross-language processing logic."""
    print("🌐 Testing cross-language processing logic...")
    
    try:
        from services.cross_language_processor import cross_language_processor
        
        test_cases = [
            ("Tamil", "English", True),   # Should translate
            ("English", "Tamil", True),   # Should translate  
            ("Tamil", "Tamil", False),    # Should not translate
            ("English", "English", False) # Should not translate
        ]
        
        results = []
        for query_lang, data_lang, expected in test_cases:
            should_translate = cross_language_processor.should_use_translation_flow(
                query_lang, data_lang
            )
            success = should_translate == expected
            results.append(success)
            
            status = "✅" if success else "❌"
            print(f"  {status} {query_lang} -> {data_lang}: Translate: {should_translate} (expected: {expected})")
        
        success_count = sum(results)
        print(f"\n📊 Cross-Language Tests: {success_count}/{len(results)} passed")
        return success_count == len(results)
        
    except ImportError:
        print("  ⚠️  Cross-language processor not available, skipping tests")
        return True

def generate_test_report(results):
    """Generate a test report."""
    print("\n" + "=" * 70)
    print("📋 TEST REPORT SUMMARY")
    print("=" * 70)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r['passed'])
    
    for result in results:
        status = "✅ PASSED" if result['passed'] else "❌ FAILED"
        print(f"{status} - {result['name']}")
    
    print(f"\n📊 Overall Results:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Passed: {passed_tests}")
    print(f"   Failed: {total_tests - passed_tests}")
    print(f"   Success Rate: {(passed_tests / total_tests * 100):.1f}%")
    
    if passed_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED! Language routing system is working correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Please review the implementation.")
    
    print("=" * 70)

def main():
    """Main test runner function."""
    print_header()
    
    # Check dependencies
    if not check_dependencies():
        print("❌ No dependencies available. Cannot run tests.")
        return
    
    # Run all tests
    test_results = []
    
    # Unit tests
    unit_test_result = run_unit_tests()
    test_results.append({
        'name': 'Unit Tests',
        'passed': unit_test_result
    })
    
    print()
    
    # Manual language detection tests
    detection_result = test_manual_language_detection()
    test_results.append({
        'name': 'Language Detection Tests',
        'passed': detection_result
    })
    
    print()
    
    # Tamil routing tests
    routing_result = test_tamil_routing_logic()
    test_results.append({
        'name': 'Tamil Routing Tests', 
        'passed': routing_result
    })
    
    print()
    
    # Cross-language tests
    cross_lang_result = test_cross_language_logic()
    test_results.append({
        'name': 'Cross-Language Tests',
        'passed': cross_lang_result
    })
    
    # Generate report
    generate_test_report(test_results)

if __name__ == '__main__':
    main()
