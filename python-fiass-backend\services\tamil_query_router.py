"""
Tamil Query Router Service

This service handles routing Tamil queries directly to the Tamil FAISS API
for native Tamil language processing without translation.
"""

import requests
import json
import logging
from typing import Dict, Any, Optional
from services.language_utils import enhanced_language_detector, PRIORITY_LANGUAGES

# Configure logging
logger = logging.getLogger(__name__)

class TamilQueryRouter:
    """
    Service to route Tamil queries directly to the Tamil FAISS API.
    """
    
    def __init__(self, tamil_api_base_url: str = "http://localhost:5000"):
        """
        Initialize the Tamil query router.
        
        Args:
            tamil_api_base_url: Base URL for the Tamil FAISS API
        """
        self.tamil_api_base_url = tamil_api_base_url.rstrip('/')
        self.tamil_endpoint = f"{self.tamil_api_base_url}/financial_query"
        logger.info(f"Tamil Query Router initialized with endpoint: {self.tamil_endpoint}")
    
    def should_route_to_tamil_api(self, query: str, data_language: Optional[str] = None) -> bool:
        """
        Determine if a query should be routed to the Tamil API.
        
        Args:
            query: User query text
            data_language: Language of the stored data (if known)
            
        Returns:
            bool: True if query should be routed to Tamil API
        """
        # Detect query language
        query_language, confidence, _ = enhanced_language_detector.detect_language_with_confidence(query)
        
        logger.info(f"Query language detection: {query_language} (confidence: {confidence:.3f})")
        
        # Route to Tamil API if:
        # 1. Query is in Tamil with sufficient confidence
        # 2. Data is in Tamil (if known) and query is Tamil
        if query_language == 'Tamil' and confidence >= 0.1:
            if data_language is None or data_language == 'Tamil':
                logger.info("Routing to Tamil API: Tamil query detected")
                return True
        
        logger.info("Not routing to Tamil API: Using standard processing")
        return False
    
    def route_tamil_query(self, query: str, additional_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Route a Tamil query to the Tamil FAISS API.
        
        Args:
            query: Tamil query text
            additional_params: Additional parameters to pass to the API
            
        Returns:
            Dict[str, Any]: Response from Tamil API or error information
        """
        try:
            # Prepare request payload
            payload = {
                "query": query
            }
            
            # Add any additional parameters
            if additional_params:
                payload.update(additional_params)
            
            logger.info(f"Sending Tamil query to API: {query[:50]}...")
            logger.debug(f"Full payload: {payload}")
            
            # Make request to Tamil API
            response = requests.post(
                self.tamil_endpoint,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("Tamil API request successful")
                
                # Add metadata about routing
                result['routing_info'] = {
                    'routed_to_tamil_api': True,
                    'api_endpoint': self.tamil_endpoint,
                    'processing_type': 'direct_tamil_retrieval'
                }
                
                return result
            else:
                logger.error(f"Tamil API request failed with status {response.status_code}: {response.text}")
                return {
                    'error': f"Tamil API request failed with status {response.status_code}",
                    'error_type': 'tamil_api_error',
                    'routing_info': {
                        'routed_to_tamil_api': True,
                        'api_endpoint': self.tamil_endpoint,
                        'processing_type': 'direct_tamil_retrieval',
                        'error_details': response.text
                    }
                }
                
        except requests.exceptions.Timeout:
            logger.error("Tamil API request timed out")
            return {
                'error': 'Tamil API request timed out',
                'error_type': 'timeout_error',
                'routing_info': {
                    'routed_to_tamil_api': True,
                    'api_endpoint': self.tamil_endpoint,
                    'processing_type': 'direct_tamil_retrieval'
                }
            }
        except requests.exceptions.ConnectionError:
            logger.error("Could not connect to Tamil API")
            return {
                'error': 'Could not connect to Tamil API. Please ensure the Tamil FAISS service is running.',
                'error_type': 'connection_error',
                'routing_info': {
                    'routed_to_tamil_api': True,
                    'api_endpoint': self.tamil_endpoint,
                    'processing_type': 'direct_tamil_retrieval'
                }
            }
        except Exception as e:
            logger.error(f"Unexpected error routing to Tamil API: {str(e)}")
            return {
                'error': f'Unexpected error routing to Tamil API: {str(e)}',
                'error_type': 'unexpected_error',
                'routing_info': {
                    'routed_to_tamil_api': True,
                    'api_endpoint': self.tamil_endpoint,
                    'processing_type': 'direct_tamil_retrieval'
                }
            }
    
    def check_tamil_api_health(self) -> Dict[str, Any]:
        """
        Check if the Tamil API is available and healthy.
        
        Returns:
            Dict[str, Any]: Health check result
        """
        try:
            # Try a simple request to check if the API is available
            response = requests.get(
                f"{self.tamil_api_base_url}/",
                timeout=5
            )
            
            if response.status_code in [200, 404]:  # 404 is OK, means server is running
                return {
                    'healthy': True,
                    'status_code': response.status_code,
                    'endpoint': self.tamil_api_base_url
                }
            else:
                return {
                    'healthy': False,
                    'status_code': response.status_code,
                    'endpoint': self.tamil_api_base_url,
                    'error': f"Unexpected status code: {response.status_code}"
                }
                
        except requests.exceptions.ConnectionError:
            return {
                'healthy': False,
                'endpoint': self.tamil_api_base_url,
                'error': 'Connection refused - Tamil API service may not be running'
            }
        except Exception as e:
            return {
                'healthy': False,
                'endpoint': self.tamil_api_base_url,
                'error': str(e)
            }


# Global instance for easy access
tamil_router = TamilQueryRouter()
