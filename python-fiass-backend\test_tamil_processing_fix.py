#!/usr/bin/env python3
"""
Comprehensive test to verify the Tamil language processing fix
This test demonstrates the elimination of double translation for Tamil queries
"""

import requests
import json
import re

def test_tamil_processing_flows():
    """Test Tamil processing flows to verify no double translation occurs"""
    
    url = "http://localhost:5010/financial_query"
    
    print("🔧 TAMIL LANGUAGE PROCESSING FIX VERIFICATION")
    print("=" * 70)
    print("This test verifies that the double translation issue has been fixed")
    print("=" * 70)
    
    # Test 1: Tamil Query with Tamil Index (Direct Processing)
    print("\n📋 TEST 1: Tamil Query + Tamil Index = Direct Processing")
    print("Expected: No translation, direct Tamil response")
    print("-" * 50)
    
    tamil_data = {
        "query": "பழைய செயலக இராணுவ தோட்டத்தில் தீ விபத்து",
        "index_name": "tamil",
        "client_email": "<EMAIL>"
    }
    
    try:
        response = requests.post(url, json=tamil_data, headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"🔤 Query: {tamil_data['query']}")
            print(f"📊 Index: {tamil_data['index_name']}")
            print(f"🎯 Direct Tamil Processing: {result.get('direct_tamil_processing', False)}")
            print(f"🌐 Query Translated: {result.get('query_translated', False)}")
            print(f"🔄 Translation Applied: {result.get('translation_applied', False)}")
            
            cross_lang = result.get('cross_language_processing', {})
            print(f"🌏 Cross-Language Applied: {cross_lang.get('applied', False)}")
            
            # Check if response is in Tamil
            ai_response = result.get('ai_response', '')
            tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
            has_tamil = bool(tamil_pattern.search(ai_response))
            print(f"🔤 Response in Tamil: {has_tamil}")
            print(f"🤖 Response (first 100 chars): {ai_response[:100]}...")
            
            # Verify no double translation occurred
            if (result.get('direct_tamil_processing') and 
                not result.get('query_translated') and 
                not result.get('translation_applied') and 
                not cross_lang.get('applied', False) and 
                has_tamil):
                print("✅ TEST 1 PASSED: Direct Tamil processing working correctly!")
            else:
                print("❌ TEST 1 FAILED: Double translation still occurring!")
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    # Test 2: English Query with Tamil Index (Cross-Language Processing)
    print("\n📋 TEST 2: English Query + Tamil Index = Cross-Language Processing")
    print("Expected: Proper cross-language translation, English response")
    print("-" * 50)
    
    english_data = {
        "query": "What is the latest news about government policies?",
        "index_name": "tamil",
        "client_email": "<EMAIL>"
    }
    
    try:
        response = requests.post(url, json=english_data, headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"🔤 Query: {english_data['query']}")
            print(f"📊 Index: {english_data['index_name']}")
            print(f"🎯 Direct Tamil Processing: {result.get('direct_tamil_processing', False)}")
            print(f"🌐 Query Translated: {result.get('query_translated', False)}")
            print(f"🔄 Translation Applied: {result.get('translation_applied', False)}")
            
            cross_lang = result.get('cross_language_processing', {})
            print(f"🌏 Cross-Language Applied: {cross_lang.get('applied', False)}")
            
            # Check if response is in English
            ai_response = result.get('ai_response', '')
            tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
            has_tamil = bool(tamil_pattern.search(ai_response))
            print(f"🔤 Response in Tamil: {has_tamil}")
            print(f"🤖 Response (first 100 chars): {ai_response[:100]}...")
            
            # Verify cross-language processing worked correctly
            if (not result.get('direct_tamil_processing') and 
                cross_lang.get('applied', False) and 
                not has_tamil):
                print("✅ TEST 2 PASSED: Cross-language processing working correctly!")
            else:
                print("❌ TEST 2 FAILED: Cross-language processing not working as expected!")
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print("\n" + "=" * 70)
    print("📋 SUMMARY OF FIXES")
    print("=" * 70)
    print("✅ BEFORE: Tamil queries underwent double translation (Tamil → English → Tamil)")
    print("✅ AFTER: Tamil queries get direct Tamil responses (no translation)")
    print("✅ BEFORE: Language detection was overridden by client preferences")
    print("✅ AFTER: Actual language detection takes precedence")
    print("✅ BEFORE: Cross-language processor was always invoked")
    print("✅ AFTER: Cross-language processor skipped for direct Tamil processing")
    print("\n🎯 RESULT: Improved response quality and faster processing for Tamil queries!")

if __name__ == "__main__":
    test_tamil_processing_flows()
