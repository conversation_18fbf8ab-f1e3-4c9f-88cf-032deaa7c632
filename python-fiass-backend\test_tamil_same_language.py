#!/usr/bin/env python3
"""
Test script to verify that Tamil queries on Tamil data with Tamil target language
do not trigger unnecessary translation.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_same_language_logic():
    """Test the same language detection logic."""
    print("🧪 Testing Same Language Logic")
    print("=" * 50)
    
    try:
        from services.language_utils import LANGUAGE_CODE_MAP
        
        # Test scenarios
        test_cases = [
            {
                "name": "Tamil query, Tamil data, Tamil target",
                "query_lang": "Tamil",
                "data_lang": "Tamil", 
                "target_lang": "Tamil",
                "expected_same": True
            },
            {
                "name": "Tamil query, Tamil data, ta target",
                "query_lang": "Tamil",
                "data_lang": "Tamil",
                "target_lang": "ta",
                "expected_same": True
            },
            {
                "name": "Tamil query, English data, Tamil target",
                "query_lang": "Tamil",
                "data_lang": "English",
                "target_lang": "Tamil", 
                "expected_same": False
            },
            {
                "name": "English query, Tamil data, English target",
                "query_lang": "English",
                "data_lang": "Tamil",
                "target_lang": "English",
                "expected_same": False
            }
        ]
        
        for case in test_cases:
            # Simulate the logic from the main code
            selected_language = case["query_lang"]
            data_language = case["data_lang"]
            target_language = case["target_lang"]
            
            final_response_language = target_language or selected_language
            
            # Convert language names to codes for comparison
            selected_lang_code = LANGUAGE_CODE_MAP.get(selected_language, selected_language.lower() if selected_language else 'en')
            data_lang_code = LANGUAGE_CODE_MAP.get(data_language, data_language.lower() if data_language else 'en')
            target_lang_code = LANGUAGE_CODE_MAP.get(final_response_language, final_response_language.lower() if final_response_language else 'en')
            
            # Check if all languages are the same
            all_languages_same = (selected_lang_code == data_lang_code == target_lang_code)
            
            # Verify result
            status = "✅" if all_languages_same == case["expected_same"] else "❌"
            print(f"{status} {case['name']}")
            print(f"    Query: {selected_language} ({selected_lang_code})")
            print(f"    Data:  {data_language} ({data_lang_code})")
            print(f"    Target: {final_response_language} ({target_lang_code})")
            print(f"    Same: {all_languages_same} (expected: {case['expected_same']})")
            print()
        
        return True
        
    except ImportError as e:
        print(f"❌ Language utils not available: {e}")
        return False

def test_cross_language_processor_logic():
    """Test the cross-language processor logic with same language scenarios."""
    print("🌐 Testing Cross-Language Processor with Same Languages")
    print("=" * 60)
    
    try:
        from services.cross_language_processor import cross_language_processor
        
        # Test cases that should NOT trigger translation
        no_translation_cases = [
            ("Tamil", "Tamil", "Tamil"),
            ("Tamil", "Tamil", "ta"),
            ("Tamil", "Tamil", None),  # None means same as query
            ("English", "English", "English"),
            ("English", "English", "en"),
            ("English", "English", None),
        ]
        
        # Test cases that SHOULD trigger translation
        translation_cases = [
            ("Tamil", "English", "Tamil"),
            ("English", "Tamil", "English"),
            ("Tamil", "Tamil", "English"),  # User wants English response
            ("English", "English", "Tamil"),  # User wants Tamil response
        ]
        
        print("Cases that should NOT trigger translation:")
        for query_lang, data_lang, target_lang in no_translation_cases:
            should_translate = cross_language_processor.should_use_translation_flow(
                query_lang, data_lang, target_lang
            )
            status = "✅" if not should_translate else "❌"
            print(f"  {status} Query:{query_lang} Data:{data_lang} Target:{target_lang} -> Translate:{should_translate}")
        
        print("\nCases that SHOULD trigger translation:")
        for query_lang, data_lang, target_lang in translation_cases:
            should_translate = cross_language_processor.should_use_translation_flow(
                query_lang, data_lang, target_lang
            )
            status = "✅" if should_translate else "❌"
            print(f"  {status} Query:{query_lang} Data:{data_lang} Target:{target_lang} -> Translate:{should_translate}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Cross-language processor not available: {e}")
        return False

def main():
    """Main test function."""
    print("🔧 TESTING TAMIL SAME-LANGUAGE LOGIC FIX")
    print("=" * 70)
    print("This test verifies that Tamil queries on Tamil data with Tamil target")
    print("do not trigger unnecessary translation.")
    print("=" * 70)
    
    # Run tests
    results = []
    
    results.append(("Same Language Logic", test_same_language_logic()))
    results.append(("Cross-Language Processor", test_cross_language_processor_logic()))
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Same-language logic is working correctly.")
        print("\n💡 Expected behavior:")
        print("• Tamil query + Tamil data + Tamil target = NO translation")
        print("• Tamil query + Tamil data + 'ta' target = NO translation") 
        print("• Different language combinations = Translation as needed")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")

if __name__ == '__main__':
    main()
